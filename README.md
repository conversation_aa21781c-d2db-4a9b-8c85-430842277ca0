# 报告列表页 API 测试前端Demo

这是一个用于测试智能安全防控平台报告列表API的前端演示页面。

## 功能特性

- 🔧 **API配置**: 可配置后端API基础URL和Authorization token
- 📊 **参数设置**: 支持设置分页参数（page, size）和搜索关键词
- 🌐 **实时测试**: 发送真实的HTTP请求到后端API
- 📋 **响应展示**: 以JSON格式展示完整的API响应
- 📈 **数据表格**: 将成功响应的数据以表格形式美观展示
- 🎨 **友好界面**: 现代化的UI设计，支持中文

## 文件结构

```
test-api/
├── index.html          # 主要的测试页面
├── server.js           # 简单的HTTP服务器
├── 报告列表页.yaml      # API接口文档
└── README.md           # 说明文档
```

## 快速开始

### 1. 启动前端服务器

```bash
node server.js
```

服务器将在 `http://localhost:3000` 启动。

### 2. 打开浏览器

访问 `http://localhost:3000` 即可看到测试页面。

### 3. 配置API

在页面上配置以下信息：

- **API基础URL**: 你的后端服务地址，例如 `http://localhost:8080`
- **Authorization Token**: 从接口文档中获取的Bearer token
- **查询参数**: 
  - 页码 (page): 当前页码，默认为1
  - 每页记录数 (size): 每页显示的记录数，默认为10
  - 搜索关键词 (keyword): 可选，用于搜索项目名称或任务名称

### 4. 测试API

点击"发送请求"按钮即可测试API接口。

## API接口信息

- **接口路径**: `GET /sast/reports/list`
- **功能**: 获取报告列表（分页）
- **支持参数**:
  - `page`: 当前页码 (可选，默认1)
  - `size`: 每页记录数 (可选，默认10，最大100)
  - `keyword`: 搜索关键词 (可选)
- **请求头**: 需要 `Authorization` 头包含Bearer token

## 响应数据结构

成功响应示例：
```json
{
  "code": 0,
  "msg": "string",
  "data": {
    "records": [
      {
        "id": "1",
        "reportName": "漏洞测试分析报告",
        "projectName": "cms系统项目",
        "taskName": "漏洞检测测试",
        "defectCount": 134,
        "vulnerabilityStats": {
          "highRisk": 100,
          "mediumRisk": 30,
          "lowRisk": 4
        },
        "verificationSuccess": 130,
        "verificationFailure": 4,
        "reportGenerationTime": "2024-03-12 12:00:00"
      }
    ],
    "total": 10,
    "size": 10,
    "current": 1,
    "pages": 1
  }
}
```

## 使用说明

1. **配置后端地址**: 确保你的后端服务正在运行，并在"API基础URL"中输入正确的地址
2. **设置认证**: 在"Authorization Token"字段中输入有效的Bearer token
3. **调整参数**: 根据需要设置分页参数和搜索关键词
4. **发送请求**: 点击"发送请求"按钮
5. **查看结果**: 
   - 请求状态会显示在"API响应"部分
   - 完整的JSON响应会显示在响应框中
   - 如果请求成功，数据会以表格形式展示

## 故障排除

### 常见问题

1. **CORS错误**: 
   - 确保后端服务配置了正确的CORS设置
   - 允许来自 `http://localhost:3000` 的请求

2. **401 Unauthorized**:
   - 检查Authorization token是否正确
   - 确认token没有过期

3. **连接失败**:
   - 确认后端服务正在运行
   - 检查API基础URL是否正确
   - 确认网络连接正常

4. **404 Not Found**:
   - 确认API路径 `/sast/reports/list` 在后端正确实现

### 调试技巧

- 打开浏览器开发者工具 (F12) 查看网络请求详情
- 查看控制台 (Console) 获取详细的错误信息
- 使用"清空响应"按钮重置页面状态

## 技术栈

- **前端**: 原生HTML + CSS + JavaScript
- **服务器**: Node.js HTTP模块
- **API通信**: Fetch API
- **样式**: 现代CSS Grid/Flexbox布局

## 扩展功能

这个demo可以轻松扩展以支持：

- 更多API接口测试
- 请求历史记录
- 响应数据导出
- 自动化测试脚本
- 性能监控

## 许可证

此项目仅用于API测试目的。
