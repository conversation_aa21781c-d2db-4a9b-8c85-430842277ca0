<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>报告列表页 - API测试</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background-color: #f5f5f5;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 20px;
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
            color: #333;
        }
        
        .api-config {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 6px;
            margin-bottom: 20px;
        }
        
        .form-group {
            margin-bottom: 15px;
        }
        
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #555;
        }
        
        input, select {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        
        .form-row {
            display: flex;
            gap: 15px;
        }
        
        .form-row .form-group {
            flex: 1;
        }
        
        button {
            background: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            margin-right: 10px;
        }
        
        button:hover {
            background: #0056b3;
        }
        
        .clear-btn {
            background: #6c757d;
        }
        
        .clear-btn:hover {
            background: #545b62;
        }
        
        .response-section {
            margin-top: 20px;
        }
        
        .response-box {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            min-height: 200px;
            font-family: 'Courier New', monospace;
            white-space: pre-wrap;
            overflow-x: auto;
        }
        
        .status-success {
            color: #28a745;
            font-weight: bold;
        }
        
        .status-error {
            color: #dc3545;
            font-weight: bold;
        }
        
        .report-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 15px;
        }
        
        .report-table th,
        .report-table td {
            border: 1px solid #dee2e6;
            padding: 8px 12px;
            text-align: left;
        }
        
        .report-table th {
            background: #f8f9fa;
            font-weight: bold;
        }
        
        .risk-high { color: #dc3545; font-weight: bold; }
        .risk-medium { color: #ffc107; font-weight: bold; }
        .risk-low { color: #28a745; font-weight: bold; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>智能安全防控平台 - 报告列表API测试</h1>
            <p>测试接口：GET /sast/reports/list</p>
        </div>
        
        <div class="api-config">
            <h3>API配置</h3>
            <div class="form-group">
                <label for="baseUrl">API基础URL:</label>
                <input type="text" id="baseUrl" placeholder="例如: http://localhost:8080" value="http://localhost:8080">
            </div>
            
            <div class="form-group">
                <label for="authorization">Authorization Token:</label>
                <input type="text" id="authorization" placeholder="Bearer token..." 
                       value="Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiIxIiwicm5TdHIiOiJ2blJ3dmx2M1lQVkQycFJ0a3ZqU2JFcmtNSVRnYVR6MyIsInVzZXJJZCI6IjEiLCJ1c2VyTmFtZSI6ImFkbWluIiwib3JnSWQiOiIxMDAiLCJvcmdOYW1lIjoi6YeN5bqG5biC6L2o6YGT5Lqk6YCa77yI6ZuG5Zui77yJ5pyJ6ZmQ5YWs5Y-4In0.0bueZdo9qQB6U3l-JBzllgdpa6GUxpaK0Zaj__vMZ_0">
            </div>
            
            <h4>查询参数</h4>
            <div class="form-row">
                <div class="form-group">
                    <label for="page">页码 (page):</label>
                    <input type="number" id="page" min="1" value="1">
                </div>
                <div class="form-group">
                    <label for="size">每页记录数 (size):</label>
                    <input type="number" id="size" min="1" max="100" value="10">
                </div>
                <div class="form-group">
                    <label for="keyword">搜索关键词 (keyword):</label>
                    <input type="text" id="keyword" placeholder="项目名称或任务名称">
                </div>
            </div>
            
            <div style="margin-top: 20px;">
                <button onclick="testAPI()">发送请求</button>
                <button class="clear-btn" onclick="clearResponse()">清空响应</button>
            </div>
        </div>
        
        <div class="response-section">
            <h3>API响应</h3>
            <div id="status"></div>
            <div id="response" class="response-box">点击"发送请求"按钮测试API...</div>
        </div>
        
        <div id="reportTable" style="display: none;">
            <h3>报告列表</h3>
            <table class="report-table">
                <thead>
                    <tr>
                        <th>报告ID</th>
                        <th>报告名称</th>
                        <th>项目名称</th>
                        <th>任务名称</th>
                        <th>缺陷数量</th>
                        <th>高危</th>
                        <th>中危</th>
                        <th>低危</th>
                        <th>验证成功</th>
                        <th>验证失败</th>
                        <th>生成时间</th>
                    </tr>
                </thead>
                <tbody id="reportTableBody">
                </tbody>
            </table>
        </div>
    </div>

    <script>
        async function testAPI() {
            const baseUrl = document.getElementById('baseUrl').value.trim();
            const authorization = document.getElementById('authorization').value.trim();
            const page = document.getElementById('page').value;
            const size = document.getElementById('size').value;
            const keyword = document.getElementById('keyword').value.trim();
            
            if (!baseUrl) {
                alert('请输入API基础URL');
                return;
            }
            
            // 构建查询参数
            const params = new URLSearchParams();
            if (page) params.append('page', page);
            if (size) params.append('size', size);
            if (keyword) params.append('keyword', keyword);
            
            const url = `${baseUrl}/sast/reports/list?${params.toString()}`;
            
            const statusDiv = document.getElementById('status');
            const responseDiv = document.getElementById('response');
            const reportTableDiv = document.getElementById('reportTable');
            
            statusDiv.innerHTML = '正在发送请求...';
            responseDiv.textContent = '请求中...';
            reportTableDiv.style.display = 'none';
            
            try {
                const headers = {
                    'Content-Type': 'application/json'
                };
                
                if (authorization) {
                    headers['Authorization'] = authorization;
                }
                
                console.log('发送请求到:', url);
                console.log('请求头:', headers);
                
                const response = await fetch(url, {
                    method: 'GET',
                    headers: headers,
                    mode: 'cors'
                });
                
                const responseText = await response.text();
                let responseData;
                
                try {
                    responseData = JSON.parse(responseText);
                } catch (e) {
                    responseData = responseText;
                }
                
                statusDiv.innerHTML = `<span class="${response.ok ? 'status-success' : 'status-error'}">
                    状态: ${response.status} ${response.statusText}
                </span>`;
                
                responseDiv.textContent = JSON.stringify(responseData, null, 2);
                
                // 如果响应成功且有数据，显示表格
                if (response.ok && responseData && responseData.code === 0 && responseData.data && responseData.data.records) {
                    displayReportTable(responseData.data);
                }
                
            } catch (error) {
                statusDiv.innerHTML = `<span class="status-error">请求失败: ${error.message}</span>`;
                responseDiv.textContent = `错误详情:\n${error.stack || error.message}`;
                console.error('API请求错误:', error);
            }
        }
        
        function displayReportTable(data) {
            const reportTableDiv = document.getElementById('reportTable');
            const tbody = document.getElementById('reportTableBody');
            
            tbody.innerHTML = '';
            
            data.records.forEach(report => {
                const row = tbody.insertRow();
                row.innerHTML = `
                    <td>${report.id || '-'}</td>
                    <td>${report.reportName || '-'}</td>
                    <td>${report.projectName || '-'}</td>
                    <td>${report.taskName || '-'}</td>
                    <td>${report.defectCount || 0}</td>
                    <td class="risk-high">${report.vulnerabilityStats?.highRisk || 0}</td>
                    <td class="risk-medium">${report.vulnerabilityStats?.mediumRisk || 0}</td>
                    <td class="risk-low">${report.vulnerabilityStats?.lowRisk || 0}</td>
                    <td>${report.verificationSuccess || 0}</td>
                    <td>${report.verificationFailure || 0}</td>
                    <td>${report.reportGenerationTime || '-'}</td>
                `;
            });
            
            reportTableDiv.style.display = 'block';
        }
        
        function clearResponse() {
            document.getElementById('status').innerHTML = '';
            document.getElementById('response').textContent = '点击"发送请求"按钮测试API...';
            document.getElementById('reportTable').style.display = 'none';
        }
        
        // 页面加载时的提示
        window.onload = function() {
            console.log('API测试页面已加载');
            console.log('请确保：');
            console.log('1. 后端服务已启动');
            console.log('2. API基础URL正确');
            console.log('3. 如有跨域问题，请配置CORS');
        };
    </script>
</body>
</html>
